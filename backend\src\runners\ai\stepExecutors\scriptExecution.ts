import { ExecutionLog, ScriptStep, getDefaultVariableName } from '@rpa-project/shared';
import { StepExecutionResult } from '../../base';
import ivm from 'isolated-vm';

/**
 * Script execution step executor for AIRunner
 */

export interface ScriptExecutorContext {
  variables: Record<string, any>;
  onLog: (log: Omit<ExecutionLog, 'timestamp'>) => void;
  interpolateVariables: (text: string, variables: Record<string, any>) => string;
}

/**
 * Execute script step - runs JavaScript code in a secure sandbox
 */
export async function executeScript(
  step: ScriptStep,
  context: ScriptExecutorContext,
  stepIndex?: number
): Promise<StepExecutionResult> {
  const { variables, onLog } = context;

  let isolate: ivm.Isolate | null = null;
  let logCallback: ivm.Callback | null = null;

  try {
    onLog({
      level: 'info',
      message: 'Starting script execution...',
      stepId: step.id
    });

    // Create isolated VM context
    isolate = new ivm.Isolate({ memoryLimit: 128 }); // 128MB memory limit
    const vmContext = await isolate.createContext();

    // Set timeout (default 60 seconds)
    const timeout = step.timeout || 60000;

    onLog({
      level: 'info',
      message: `Executing script with ${timeout}ms timeout...`,
      stepId: step.id
    });

    // Create a safe copy of variables (deep clone to prevent reference issues)
    const safeVariables = JSON.parse(JSON.stringify(variables));

    // Set up the context with variables
    await vmContext.global.set('variables', new ivm.ExternalCopy(safeVariables).copyInto());

    // Add basic console.log functionality for debugging
    const logs: string[] = [];

    // Create a callback function that can be called from the isolated context
    logCallback = new ivm.Callback((...args: any[]) => {
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ');
      logs.push(message);
    });

    // Set up console object with the callback
    await vmContext.global.set('console', new ivm.ExternalCopy({
      log: logCallback
    }).copyInto());

    // Wrap user code in a function that returns the result
    const wrappedCode = `
      (function() {
        try {
          ${step.code}
        } catch (error) {
          throw new Error('Script execution error: ' + error.message);
        }
      })()
    `;

    // Execute the script
    const result = await vmContext.eval(wrappedCode, { timeout });

    // Get the actual result value
    let scriptResult;
    if (result && typeof result.copy === 'function') {
      scriptResult = result.copy();
    } else {
      scriptResult = result;
    }

    // Log any console output from the script
    if (logs.length > 0) {
      onLog({
        level: 'info',
        message: `Script console output: ${logs.join(', ')}`,
        stepId: step.id
      });
    }

    // Store result in variable
    const variableName = step.variableName || getDefaultVariableName('script', stepIndex);
    variables[variableName] = scriptResult;

    onLog({
      level: 'info',
      message: `Script execution completed. Result stored in variable: ${variableName}`,
      stepId: step.id,
      data: {
        [variableName]: typeof scriptResult === 'object'
          ? JSON.stringify(scriptResult).substring(0, 200) + (JSON.stringify(scriptResult).length > 200 ? '...' : '')
          : String(scriptResult).substring(0, 200) + (String(scriptResult).length > 200 ? '...' : '')
      }
    });

    return {
      success: true,
      variables: { [variableName]: scriptResult }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    onLog({
      level: 'error',
      message: `Script execution failed: ${errorMessage}`,
      stepId: step.id
    });

    return {
      success: false,
      error: errorMessage
    };
  } finally {
    // Clean up resources
    if (isolate) {
      isolate.dispose();
    }
  }
}
