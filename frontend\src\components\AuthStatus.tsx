import React, { useState, useEffect } from 'react';
import { authService, AuthState } from '../services/auth';

interface AuthStatusProps {
  className?: string;
}

export const AuthStatus: React.FC<AuthStatusProps> = ({ className = '' }) => {
  const [authState, setAuthState] = useState<AuthState>(authService.getAuthState());
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const unsubscribe = authService.subscribe(setAuthState);
    return unsubscribe;
  }, []);

  const handleLogout = () => {
    authService.logout();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'text-red-600 bg-red-100';
      case 'operator': return 'text-blue-600 bg-blue-100';
      case 'viewer': return 'text-green-600 bg-green-100';
      case 'api': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (!authState.isAuthenticated) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
        <span className="text-sm text-gray-600">Ej inloggad</span>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        className="sidebar-user-button"
        onClick={() => setShowDetails(!showDetails)}
      >
        <div className="sidebar-user-info">
          <div className="sidebar-user-avatar">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div className="sidebar-user-details">
            <span className="sidebar-user-name">{authState.user?.username}</span>
            <span className="sidebar-user-role">{authState.user?.role}</span>
          </div>
        </div>
      </button>

      {showDetails && (
        <div className="sidebar-user-popup">
          <div className="sidebar-user-popup-content">
            <div className="sidebar-user-popup-header">
              <div className="sidebar-user-popup-avatar">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div className="sidebar-user-popup-info">
                <div className="sidebar-user-popup-name">{authState.user?.username}</div>
                <div className={`sidebar-user-popup-role ${getRoleColor(authState.user?.role || '')}`}>
                  {authState.user?.role}
                </div>
              </div>
            </div>



            <div className="sidebar-user-popup-section">
              <div className="sidebar-user-popup-label">Behörigheter:</div>
              <div className="sidebar-user-popup-permissions">
                {authService.isAdmin() && (
                  <div className="sidebar-user-popup-permission admin">✓ Administratör</div>
                )}
                {authService.canOperate() && (
                  <div className="sidebar-user-popup-permission operator">✓ Kan köra flöden</div>
                )}
                {authService.isViewer() && (
                  <div className="sidebar-user-popup-permission viewer">✓ Endast läsåtkomst</div>
                )}
              </div>
            </div>

            <div className="sidebar-user-popup-footer">
              <button
                onClick={handleLogout}
                className="action-button secondary sidebar-logout-button"
              >
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Logga ut
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthStatus;
